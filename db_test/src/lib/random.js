import { faker } from "@faker-js/faker";

export function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function randomFloat(min, max, decimals = 2) {
  return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

export function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

export function randomChoices(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

export const firstNames = Array.from({ length: 50 }, () =>
  faker.person.firstName()
);
export const lastNames = Array.from({ length: 50 }, () =>
  faker.person.lastName()
);
export const passwords = Array.from({ length: 20 }, () =>
  faker.internet.password()
);
export const phoneNumbers = Array.from({ length: 30 }, () =>
  faker.phone.number()
);
export const birthDates = Array.from({ length: 40 }, () =>
  faker.date.birthdate()
);
